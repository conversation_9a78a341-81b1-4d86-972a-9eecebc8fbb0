<!DOCTYPE html>
<html lang="en">
  <head>
    <title>po</title>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <link
      href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900"
      rel="stylesheet"
    />

    <link rel="stylesheet" href="css/open-iconic-bootstrap.min.css" />
    <link rel="stylesheet" href="css/animate.css" />

    <link rel="stylesheet" href="css/owl.carousel.min.css" />
    <link rel="stylesheet" href="css/owl.theme.default.min.css" />
    <link rel="stylesheet" href="css/magnific-popup.css" />

    <link rel="stylesheet" href="css/aos.css" />

    <link rel="stylesheet" href="css/ionicons.min.css" />

    <link rel="stylesheet" href="css/flaticon.css" />
    <link rel="stylesheet" href="css/icomoon.css" />
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body data-spy="scroll" data-target=".site-navbar-target" data-offset="300">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <nav
      class="navbar navbar-expand-lg navbar-dark ftco_navbar ftco-navbar-light site-navbar-target"
      id="ftco-navbar"
    >
      <div class="container">
        <a class="navbar-brand" href="index.html">kbics</a>
        <button
          class="navbar-toggler js-fh5co-nav-toggle fh5co-nav-toggle"
          type="button"
          data-toggle="collapse"
          data-target="#ftco-nav"
          aria-controls="ftco-nav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="oi oi-menu"></span> Menu
        </button>

        <div class="collapse navbar-collapse" id="ftco-nav">
          <ul class="navbar-nav nav ml-auto">
            <li class="nav-item">
              <a href="#home-section" class="nav-link"><span>Home</span></a>
            </li>
            <li class="nav-item">
              <a href="#about-section" class="nav-link"><span>About</span></a>
            </li>
            <li class="nav-item">
              <a href="#resume-section" class="nav-link"><span>Resume</span></a>
            </li>
            <li class="nav-item">
              <a href="#services-section" class="nav-link"
                ><span>Services</span></a
              >
            </li>
            <li class="nav-item">
              <a href="#skills-section" class="nav-link"><span>Skills</span></a>
            </li>
            <li class="nav-item">
              <a href="#projects-section" class="nav-link"
                ><span>Projects</span></a
              >
            </li>
            <li class="nav-item">
              <a href="#blog-section" class="nav-link"><span>My Blog</span></a>
            </li>
            <li class="nav-item">
              <a href="#contact-section" class="nav-link"
                ><span>Contact</span></a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>
    <section id="home-section" class="hero">
      <div class="home-slider owl-carousel">
        <div class="slider-item">
          <div class="overlay"></div>
          <div class="container">
            <div class="row d-flex slider-text align-items-center" data-scrollax-parent="true">
              <!-- Text Column - Similar to About Me structure -->
              <div class="col-md-6 col-lg-7 d-flex align-items-center ftco-animate" data-scrollax=" properties: { translateY: '70%' }">
                <div class="text w-100">
                  <span class="subheading">Hello!</span>
                  <h1 class="mb-4 mt-3">I'm <span>Kibru Michael</span></h1>
                  <h2 class="mb-4">A Freelance Full Stack Developer</h2>
                  <p>
                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                    <a href="#" class="btn btn-white btn-outline-white py-3 px-4">My works</a>
                  </p>
                </div>
              </div>

              <!-- Image Column - Similar to About Me structure -->
              <div class="col-md-6 col-lg-5 d-flex align-items-center justify-content-center ftco-animate">
                <div class="home-image-container d-flex align-items-center justify-content-center">
                  <div class="profile-image-wrapper">
                    <img src="images/wef.gif" alt="Profile" class="profile-image" />
                    <div class="profile-overlay"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="slider-item">
          <div class="overlay"></div>
          <div class="container">
            <div class="row d-flex slider-text align-items-center" data-scrollax-parent="true">
              <!-- Text Column - Similar to About Me structure -->
              <div class="col-md-6 col-lg-7 d-flex align-items-center ftco-animate" data-scrollax=" properties: { translateY: '70%' }">
                <div class="text w-100">
                  <span class="subheading">Hello!</span>
                  <h1 class="mb-4 mt-3">I'm a <span>Full Stack Developer</span> based in Addis Ababa</h1>
                  <p>
                    <a href="#" class="btn btn-primary py-3 px-4">Hire me</a>
                    <a href="#" class="btn btn-white btn-outline-white py-3 px-4">My works</a>
                  </p>
                </div>
              </div>

              <!-- Image Column - Similar to About Me structure -->
              <div class="col-md-6 col-lg-5 d-flex align-items-center justify-content-center ftco-animate">
                <div class="home-image-container d-flex align-items-center justify-content-center">
                  <div class="profile-image-wrapper">
                    <img src="images/log.png" alt="Profile" class="profile-image" />
                    <div class="profile-overlay"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-about img ftco-section ftco-no-pb" id="about-section">
      <div class="container">
        <!-- The blogPostsContainer has been removed from here -->
        <div class="row d-flex">
          <!-- Image Column -->
          <div class="col-md-6 col-lg-5 d-flex ftco-animate">
            <div class="img-about img d-flex align-items-stretch">
              <div class="overlay"></div>
              <!--
                You can use a background image for the div as per the template's style,
                or a direct <img> tag. Using background-image is common in this template.
                Replace 'images/bg_1.jpg' with your actual profile picture.
              -->
              <div class="img d-flex align-self-stretch align-items-center" style="background-image:url(images/kebnew.png);">
                <!-- Alternative: <img src="images/your-profile-image.jpg" alt="Kibru Michael" class="img-fluid"> -->
              </div>
            </div>
          </div>

          <!-- Text Column -->
          <div class="col-md-6 col-lg-7 pl-lg-5 pb-5 ftco-animate">
            <div class="row justify-content-start pb-3">
              <div class="col-md-12 heading-section ftco-animate">
                <h1 class="big">About</h1>
                <h2 class="mb-4">About Me</h2>
                <p data-aos-delay="100">
                  Full-stack developer focused on clean code, modern tech, and user-friendly,reliable applications.
                </p>
                <ul class="about-info mt-4 px-md-0 px-2">
                  <li class="d-flex">
                    <span>Name:</span> <span>Kibru Michael</span>
                  </li>
                  <!-- <li class="d-flex">
                    <span>Date of birth:</span> <span>January 01, 1987</span>
                  </li> -->
                  <!-- <li class="d-flex">
                    <span>Address:</span>
                    <span>San Francisco CA 97987 USA</span>
                  </li> -->
                  <li class="d-flex">
                    <span>POBox:</span> <span>13908</span>
                  </li>
                  <li class="d-flex">
                    <span>Email:</span> <span><EMAIL></span>
                  </li>
                  <li class="d-flex">
                    <span>Phone: </span> <span>+************-03</span>
                  </li>
                </ul>
              </div>
            </div>
            <div class="counter-wrap ftco-animate d-flex mt-md-3">
              <div class="text">
                <p class="mb-4">
                  <span class="number" data-number="37" data-aos-delay="200">0</span>
                  <span>Project complete</span>
                </p>

              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-section ftco-no-pb" id="resume-section">
      <div class="container">
        <div class="row justify-content-center pb-5">
          <div class="col-md-10 heading-section text-center ftco-animate">
            <h1 class="big big-2">Resume</h1>
            <h2 class="mb-4">Resume</h2>
            <!-- <p>
              A small river named Duden flows by their place and supplies it
              with the necessary regelialia. It is a paradisematic country, in
              which roasted parts of sentences fly into your mouth.
            </p> -->
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>PowerBI and related certifications</h2>
              <span class="position">Cambridge University</span>
              <div class="mt-4">
                <div
                  id="resume-image-slider-1"
                  class="carousel slide"
                  data-ride="carousel"
                  style="width: 100%; margin: 0 auto"
                >
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img
                        src="images/powerBI .png"
                        class="d-block w-100"
                        alt="PowerBI"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic1.png"
                        class="d-block w-100"
                        alt="Mic1"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic2.png"
                        class="d-block w-100"
                        alt="Mic2"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>

                    <div class="carousel-item">
                      <img
                        src="images/mic3.png"
                        class="d-block w-100"
                        alt="Mic2"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                  </div>

                  <a
                    class="carousel-control-prev"
                    href="#resume-image-slider-1"
                    role="button"
                    data-slide="prev"
                  >
                    <span
                      class="carousel-control-prev-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a
                    class="carousel-control-next"
                    href="#resume-image-slider-1"
                    role="button"
                    data-slide="next"
                  >
                    <span
                      class="carousel-control-next-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
              </div>
            </div>
            <!--
            <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>Bachelor's Degree of C.A</h2>
              <span class="position">Cambridge University</span>
              <p class="mt-4">
                A small river named Duden flows by their place and supplies it
                with the necessary regelialia. It is a paradisematic country, in
                which roasted parts of sentences fly into your mouth.
              </p>
            </div> -->

            <!-- <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>Udacity &amp; Creative Director</h2>
              <span class="position">Cambridge University</span>
              <div class="mt-4">
                <div
                  id="resume-image-slider-2"
                  class="carousel slide"
                  data-ride="carousel"
                  style="width: 100%; margin: 0 auto"
                >
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img
                        src="images/web3.png"
                        class="d-block w-100"
                        alt="PowerBI"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic1.png"
                        class="d-block w-100"
                        alt="Mic1"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                  </div>

                  <a
                    class="carousel-control-prev"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="prev"
                  >
                    <span
                      class="carousel-control-prev-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a
                    class="carousel-control-next"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="next"
                  >
                    <span
                      class="carousel-control-next-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
              </div>
            </div> -->

            <!-- <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>Diploma in Computer</h2>
              <span class="position">Cambridge University</span>
              <p class="mt-4">
                A small river named Duden flows by their place and supplies it
                with the necessary regelialia. It is a paradisematic country, in
                which roasted parts of sentences fly into your mouth.
              </p>
            </div> -->
          </div>

          <div class="col-md-6">
            <!-- <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>web Development &amp; Creative Director</h2>
              <span class="position">Cambridge University</span>
              <div class="mt-4">
                <div
                  id="resume-image-slider-2"
                  class="carousel slide"
                  data-ride="carousel"
                  style="width: 100%; margin: 0 auto"
                >
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img
                        src="images/web3.png"
                        class="d-block w-100"
                        alt="PowerBI"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic1.png"
                        class="d-block w-100"
                        alt="Mic1"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                  </div>

                  <a
                    class="carousel-control-prev"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="prev"
                  >
                    <span
                      class="carousel-control-prev-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a
                    class="carousel-control-next"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="next"
                  >
                    <span
                      class="carousel-control-next-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
              </div>
            </div> -->

            <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>web Development &amp; Creative</h2>
              <span class="position">Cambridge University</span>
              <div class="mt-4">
                <div
                  id="resume-image-slider-2"
                  class="carousel slide"
                  data-ride="carousel"
                  style="width: 100%; margin: 0 auto"
                >
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img
                        src="images/web3.png"
                        class="d-block w-100"
                        alt="PowerBI"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic1.png"
                        class="d-block w-100"
                        alt="Mic1"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                  </div>

                  <a
                    class="carousel-control-prev"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="prev"
                  >
                    <span
                      class="carousel-control-prev-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a
                    class="carousel-control-next"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="next"
                  >
                    <span
                      class="carousel-control-next-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
              </div>
            </div>

            <!-- <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>AI Developer</h2>
              <span class="position">Cambridge University</span>
              <p class="mt-4">
                A small river named Duden flows by their place and supplies it
                with the necessary regelialia. It is a paradisematic country, in
                which roasted parts of sentences fly into your mouth.
              </p>
            </div> -->

            <!-- <div class="resume-wrap ftco-animate">
              <span class="date">2014-2015</span>
              <h2>AI Development</h2>
              <span class="position">Cambridge University</span>
              <div class="mt-4">
                <div
                  id="resume-image-slider-2"
                  class="carousel slide"
                  data-ride="carousel"
                  style="width: 100%; margin: 0 auto"
                >
                  <div class="carousel-inner">
                    <div class="carousel-item active">
                      <img
                        src="images/web3.png"
                        class="d-block w-100"
                        alt="PowerBI"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                    <div class="carousel-item">
                      <img
                        src="images/mic1.png"
                        class="d-block w-100"
                        alt="Mic1"
                        style="height: auto; border-radius: 12px"
                      />
                    </div>
                  </div>

                  <a
                    class="carousel-control-prev"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="prev"
                  >
                    <span
                      class="carousel-control-prev-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Previous</span>
                  </a>
                  <a
                    class="carousel-control-next"
                    href="#resume-image-slider-2"
                    role="button"
                    data-slide="next"
                  >
                    <span
                      class="carousel-control-next-icon"
                      aria-hidden="true"
                    ></span>
                    <span class="sr-only">Next</span>
                  </a>
                </div>
              </div>
            </div> -->

            <!--
            <div class="resume-wrap ftco-animate">
              <span class="date">2017-2018</span>
              <h2>UI/UX Designer</h2>
              <span class="position">Cambridge University</span>
              <p class="mt-4">
                A small river named Duden flows by their place and supplies it
                with the necessary regelialia. It is a paradisematic country, in
                which roasted parts of sentences fly into your mouth.
              </p>
            </div> -->
          </div>
        </div>
        <div class="row justify-content-center mt-5">
          <div class="col-md-6 text-center ftco-animate">
            <p>
              <a href="#" class="btn btn-primary py-4 px-5 d-none"
                >Download CV</a
              >
            </p>
            <p>
              <a href="#" class="btn btn-secondary py-4 px-5 d-none"
                >Download CV</a
              >
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-section" id="services-section">
      <div class="container">
        <div class="row justify-content-center py-5 mt-5">
          <div class="col-md-12 heading-section text-center ftco-animate">
            <h1 class="big big-2">Services</h1>
            <h2 class="mb-4">Services</h2>
            <!-- <p>
              Far far away, behind the word mountains, far from the countries
              Vokalia and Consonantia
            </p> -->
          </div>
        </div>

        <div class="row">
          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <!-- Replace the <i> tag with an <img> tag -->
                <img
                  src="images/ux.png"
                  alt="UI/UX Design Icon"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">UI/UX Design</h3>
              </div>
            </a>
          </div>

          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <!-- Replace the <i> tag with an <img> tag -->
                <img
                  src="images/full.png"
                  alt="Full-Stack Development Icon"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">Full-Stack Development</h3>
              </div>
            </a>
          </div>

          <!-- <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <i class="flaticon-flasks"></i>
              </span>
              <div class="desc">
                <h3 class="mb-5">Full-Stack Development</h3>
              </div>
            </a>
          </div> -->
          <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <!-- Replace the <i> tag with an <img> tag -->
                <img
                  src="images/data-v.png"
                  alt="Dashboard Development Icon"
                  class="service-png-icon"
                />
              </span>
              <div class="desc">
                <h3 class="mb-5">Dashboard Development</h3>
              </div>
            </a>
          </div>

          <!-- <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <i class="flaticon-ideas"></i>
              </span>
              <div class="desc">
                <h3 class="mb-5">Dashboard Development</h3>
              </div>
            </a>
          </div> -->

          <!-- <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <i class="flaticon-analysis"></i>
              </span>
              <div class="desc">
                <h3 class="mb-5">Mobile App Development</h3>
              </div>
            </a>
          </div> -->
          <!-- <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <i class="flaticon-flasks"></i>
              </span>
              <div class="desc">
                <h3 class="mb-5">Dashboard Development</h3>
              </div>
            </a>
          </div> -->
          <!-- <div class="col-md-4 text-center d-flex ftco-animate">
            <a href="#" class="services-1">
              <span class="icon">
                <i class="flaticon-ideas"></i>
              </span>
              <div class="desc">
                <h3 class="mb-5">SaaS Development</h3>
              </div>
            </a>
          </div> -->
        </div>
      </div>
    </section>

    <section class="ftco-section" id="skills-section">
      <div class="container">
        <div class="row justify-content-center pb-5">
          <div class="col-md-12 heading-section text-center ftco-animate">
            <h1 class="big big-2">Skills</h1>
            <h2 class="mb-4">My Skills</h2>
            <!-- <p>
              Far far away, behind the word mountains, far from the countries
              Vokalia and Consonantia
            </p> -->
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>javascript</h3>
              <div class="progress">
                <div
                  class="progress-bar color-1"
                  role="progressbar"
                  aria-valuenow="90"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 90%"
                >
                  <span>90%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>jQuery</h3>
              <div class="progress">
                <div
                  class="progress-bar color-2"
                  role="progressbar"
                  aria-valuenow="85"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 85%"
                >
                  <span>85%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>HTML5</h3>
              <div class="progress">
                <div
                  class="progress-bar color-3"
                  role="progressbar"
                  aria-valuenow="95"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 95%"
                >
                  <span>95%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>CSS3</h3>
              <div class="progress">
                <div
                  class="progress-bar color-4"
                  role="progressbar"
                  aria-valuenow="90"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 90%"
                >
                  <span>90%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>MERN Stack</h3>
              <div class="progress">
                <div
                  class="progress-bar color-4"
                  role="progressbar"
                  aria-valuenow="90"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 90%"
                >
                  <span>90%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>python</h3>
              <div class="progress">
                <div
                  class="progress-bar color-4"
                  role="progressbar"
                  aria-valuenow="90"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 80%"
                >
                  <span>80%</span>
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 animate-box">
            <div class="progress-wrap ftco-animate">
              <h3>PowerBI</h3>
              <div class="progress">
                <div
                  class="progress-bar color-6"
                  role="progressbar"
                  aria-valuenow="80"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  style="width: 80%"
                >
                  <span>80%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-section ftco-project" id="projects-section">
      <div class="container">
        <div class="row justify-content-center pb-5">
          <div class="col-md-12 heading-section text-center ftco-animate">
            <h1 class="big big-2">Projects</h1>
            <h2 class="mb-4">Our Projects</h2>
            <!-- <p>
              Far far away, behind the word mountains, far from the countries
              Vokalia and Consonantia
            </p> -->
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-4.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                <span>Web Design</span>
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/dp1.png)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Dashboard development</a></h3>
                <span>PowerBI</span>
              </div>
            </div>
          </div>

          <div class="col-md-8">
            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-1.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                <span>Web Design</span>
              </div>
            </div>

            <div
              class="project img ftco-animate d-flex justify-content-center align-items-center"
              style="background-image: url(images/project-6.jpg)"
            >
              <div class="overlay"></div>
              <div class="text text-center p-4">
                <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                <span>Web Design</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="row">
              <div class="col-md-12">
                <div
                  class="project img ftco-animate d-flex justify-content-center align-items-center"
                  style="background-image: url(images/project-2.jpg)"
                >
                  <div class="overlay"></div>
                  <div class="text text-center p-4">
                    <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                    <span>Web Design</span>
                  </div>
                </div>
              </div>
              <div class="col-md-12">
                <div
                  class="project img ftco-animate d-flex justify-content-center align-items-center"
                  style="background-image: url(images/project-3.jpg)"
                >
                  <div class="overlay"></div>
                  <div class="text text-center p-4">
                    <h3><a href="#">Branding &amp; Illustration Design</a></h3>
                    <span>Web Design</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="ftco-section" id="blog-section">
      <div class="container">
        <div class="row justify-content-center mb-5 pb-5">
          <div class="col-md-7 heading-section text-center ftco-animate">
            <h1 class="big big-2">Blog</h1>
            <h2 class="mb-4">Our Blog</h2>
            <!-- <p>
              Far far away, behind the word mountains, far from the countries
              Vokalia and Consonantia
            </p> -->
          </div>
        </div>
        <div class="row d-flex">
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry justify-content-end">
              <a href="single.html?post=1">
                <img
                  src="images/image1.jpg"
                  alt="Blog Post: The Rise of Serverless Architecture"
                  class="block-20"
                  style="
                    width: 100%; 
                    height: 350px; /* Matches the height from .block-20 CSS */
                    object-fit: cover; /* Ensures the image covers the area, similar to background-size: cover */
                    display: block; /* Ensures the image behaves as a block element */
                    transition: transform 0.3s ease;
                  "
                />
              </a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="single.html?post=1"
                    >The Rise of Serverless Architecture</a
                  >
                </h3>
                <p>
                  Serverless architecture has been gaining significant traction in the web development world, and for good reason.
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry justify-content-end">
              <a href="single.html?post=2">
                <img
                  src="images/image2.jpg"
                  alt="Blog Post: Demystifying Machine Learning for Web Developers"
                  class="block-20"
                  style="
                    width: 100%; 
                    height: 350px; /* Matches the height from .block-20 CSS */
                    object-fit: cover; /* Ensures the image covers the area, similar to background-size: cover */
                    display: block; /* Ensures the image behaves as a block element */
                    transition: transform 0.3s ease;
                  "
                />
              </a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="single.html?post=2"
                    >Demystifying Machine Learning for Web Developers</a
                  >
                </h3>
                <p>
                  The term "Machine Learning" can sound intimidating, but at its core, it's about enabling computers to learn from data without explicit programming.
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-4 d-flex ftco-animate">
            <div class="blog-entry">
              <a href="single.html?post=3">
                <img
                  src="images/image3.jpg"
                  alt="Blog Post: The Synergy Between AI and Full-Stack Development"
                  class="block-20" 
                  style="
                    width: 100%; 
                    height: 350px; /* Matches the height from .block-20 CSS */
                    object-fit: cover; /* Ensures the image covers the area, similar to background-size: cover */
                    display: block; /* Ensures the image behaves as a block element */
                    transition: transform 0.3s ease;
                  "
                />
              </a>
              <div class="text mt-3 float-right d-block">
                <div class="d-flex align-items-center mb-3 meta">
                  <p class="mb-0">
                    <span class="mr-2">June 21, 2019</span>
                    <a href="#" class="mr-2">Admin</a>
                    <a href="#" class="meta-chat"
                      ><span class="icon-chat"></span> 3</a
                    >
                  </p>
                </div>
                <h3 class="heading">
                  <a href="single.html?post=3">The Synergy Between AI and Full-Stack Development</a>
                </h3>
                <p>
                  The intersection of Artificial Intelligence (AI) and full-stack development is creating exciting new possibilities.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section
      class="ftco-section ftco-no-pt ftco-no-pb ftco-counter img"
      id="section-counter"
    >
      <div class="container">
        <div class="row d-md-flex align-items-center">
          <div
            class="col-md d-flex justify-content-center counter-wrap ftco-animate"
          >
            <div class="block-18">
              <div class="text">
                <strong class="number" data-number="10">0</strong>
                <span>Awards</span>
              </div>
            </div>
          </div>
          <div
            class="col-md d-flex justify-content-center counter-wrap ftco-animate"
          >
            <div class="block-18">
              <div class="text">
                <strong class="number" data-number="37">0</strong>
                <span>Complete Projects</span>
              </div>
            </div>
          </div>
          <div
            class="col-md d-flex justify-content-center counter-wrap ftco-animate"
          >
            <div class="block-18">
              <div class="text">
                <strong class="number" data-number="81">0</strong>
                <span>Happy Customers</span>
              </div>
            </div>
          </div>
          <div
            class="col-md d-flex justify-content-center counter-wrap ftco-animate"
          >
            <div class="block-18">
              <div class="text">
                <strong class="number" data-number="30">0</strong>
                <span>Cups of coffee</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section
      class="ftco-section ftco-hireme img margin-top"
      style="background-image: url(images/bg_1.jpg)"
    >
      <div class="container">
        <div class="row justify-content-center">
          <div class="col-md-7 ftco-animate text-center">
            <h2>I'm <span>Available</span> for freelancing</h2>

            <p class="mb-0">
              <a href="#" class="btn btn-primary py-3 px-5">Hire me</a>
            </p>
          </div>
        </div>
        </div> <!-- Closes the new row for "About Me" content -->
      </div>
    </section>

    <section
      class="ftco-section contact-section ftco-no-pb"
      id="contact-section"
    >
      <div class="container">
        <div class="row justify-content-center mb-5 pb-3">
          <div class="col-md-7 heading-section text-center ftco-animate">
            <h1 class="big big-2">Contact</h1>
            <h2 class="mb-4">Contact Me</h2>
            <!-- <p>
              Far far away, behind the word mountains, far from the countries
              Vokalia and Consonantia
            </p> -->
          </div>
        </div>

        <div class="row d-flex contact-info mb-5">
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-map-signs"></span>
              </div>
              <h3 class="mb-4">Address</h3>
              <p>Addis Abeba, Ethiopia,POB 13908.</p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-phone2"></span>
              </div>
              <h3 class="mb-4">Contact Number</h3>
              <p><a href="tel://1234567920">+************-03</a></p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-paper-plane"></span>
              </div>
              <h3 class="mb-4">Email Address</h3>
              <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
          <div class="col-md-6 col-lg-3 d-flex ftco-animate">
            <div class="align-self-stretch box p-4 text-center">
              <div
                class="icon d-flex align-items-center justify-content-center"
              >
                <span class="icon-globe"></span>
              </div>
              <h3 class="mb-4">Website</h3>
              <p><a href="#">kibrumic.com</a></p>
            </div>
          </div>
        </div>

        <div class="row no-gutters block-9">
          <div class="col-md-6 order-md-last d-flex">
            <form action="#" class="bg-light p-4 p-md-5 contact-form">
              <div class="form-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Your Name"
                />
              </div>
              <div class="form-group">
                <input
                  type="text"
                  class="form-control"
                  placeholder="Your Email"
                />
              </div>
              <div class="form-group">
                <input type="text" class="form-control" placeholder="Subject" />
              </div>
              <div class="form-group">
                <textarea
                  name=""
                  id=""
                  cols="30"
                  rows="7"
                  class="form-control"
                  placeholder="Message"
                ></textarea>
              </div>
              <div class="form-group">
                <input
                  type="submit"
                  value="Send Message"
                  class="btn btn-primary py-3 px-5"
                />
              </div>
            </form>
          </div>

          <div class="col-md-6 d-flex">
            <!-- Start of new animated message block -->
            <div
              class="contact-message-container d-flex align-items-center justify-content-center ftco-animate"
              data-animate-effect="fadeInRight"
              style="
                width: 100%;
                min-height: 350px;
                background-color: #000000; /* Changed to black like the footer */
                border-radius: 12px;
                padding: 30px;
                /* Removed box-shadow and border for a flat look */
              "
            >
              <div class="text-center">
                <div class="ftco-animate" data-animate-effect="fadeInUp">
                  <h2
                    style="
                      font-size: 2.9rem;
                      font-weight: 700;
                      color: #f96d00;
                      margin-bottom: 0.75rem;
                      line-height: 1.2;
                    "
                  >
                    Let's Connect
                  </h2>
                </div>
                <div
                  class="ftco-animate"
                  data-animate-effect="fadeInUp"
                  data-aos-delay="200"
                >
                  <p
                    style="
                      font-size: 1.1rem;
                      color: rgba(
                        255,
                        255,
                        255,
                        0.7
                      ); /* Changed for contrast against black background */
                      margin-bottom: 1.8rem;
                    "
                  >
                    Have a project in mind or just want to say hi?
                  </p>
                </div>
                <div
                  class="ftco-animate"
                  data-animate-effect="pulse"
                  data-aos-delay="400"
                >
                  <span
                    class="icon-paper-plane"
                    style="
                      font-size: 2.8rem;
                      color: #f96d00;
                      display: inline-block;
                      transform: rotate(-15deg);
                    "
                  ></span>
                </div>
              </div>
            </div>
            <!-- End of new animated message block -->
          </div>
        </div>
      </div>
    </section>

    <footer class="ftco-footer ftco-section">
      <div class="container">
        <div class="row mb-5">
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">About</h2>
              <p>
                Full-stack developer focused on clean code, modern tech, and
                user-friendly, reliable applications.
              </p>
              <ul
                class="ftco-footer-social list-unstyled float-md-left float-lft mt-5"
              >
                <li class="ftco-animate">
                  <a href="#"><span class="icon-twitter"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="#"><span class="icon-facebook"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="#"><span class="icon-instagram"></span></a>
                </li>

                <li class="ftco-animate">
                  <a href="#"><span class="icon-linkedin"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="https://github.com/KibruMichael" target="_blank"><span class="icon-github"></span></a>
                </li>
                <li class="ftco-animate">
                  <a href="https://t.me/+2gQlKrJVVE1kZDFk" target="_blank"><span class="icon-telegram"></span></a>
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4 ml-md-4">
              <h2 class="ftco-heading-2">Links</h2>
              <ul class="list-unstyled">
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Home</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>About</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Services</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Projects</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Contact</a
                  >
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Services</h2>
              <ul class="list-unstyled">
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>UI/UX Design</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Full-Stack Development</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Dashboard Development</a
                  >
                </li>
                <!-- <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Data
                    Analysis</a
                  >
                </li>
                <li>
                  <a href="#"
                    ><span class="icon-long-arrow-right mr-2"></span>Graphic
                    Design</a
                  >
                </li> -->
              </ul>
            </div>
          </div>
          <div class="col-md">
            <div class="ftco-footer-widget mb-4">
              <h2 class="ftco-heading-2">Have a Questions?</h2>
              <div class="block-23 mb-3">
                <ul>

                  <li style="display: flex; align-items: flex-start; margin-bottom: 15px;">
                    <!-- <span class="icon icon-map-marker" style="margin-right: 10px; margin-top: 5px; flex-shrink: 0;"></span> -->
                    <div class="map-embed-wrapper" style="flex-grow: 1;">
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.0900000000002!2d-122.41941558468135!3d37.77492957975915!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x8085808c1c000001%3A0x8000000000000000!2sSan%20Francisco%20City%20Hall!5e0!3m2!1sen!2sus!4v1600000000000!5m2!1sen!2sus"
                        width="100%"
                        height="250"  /* You can adjust this height */
                        style="border:0; border-radius: 8px;"
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"></iframe>
                    </div>
                  </li>
                  <li>
                    <a href="#"
                      ><span class="icon icon-phone"></span
                      ><span class="text">+251-911571403</span></a
                    >
                  </li>
                  <!-- <li>
                    <a href="#"
                      ><span class="icon icon-envelope"></span
                      ><span class="text"><EMAIL></span></a
                    >
                  </li> -->
                </ul>
              </div>
            </div>
          </div>
        </div>

      </div>
    </footer>

    <!-- loader -->
    <div id="ftco-loader" class="show fullscreen">
      <svg class="circular" width="48px" height="48px">
        <circle
          class="path-bg"
          cx="24"
          cy="24"
          r="22"
          fill="none"
          stroke-width="4"
          stroke="#eeeeee"
        />
        <circle
          class="path"
          cx="24"
          cy="24"
          r="22"
          fill="none"
          stroke-width="4"
          stroke-miterlimit="10"
          stroke="#F96D00"
        />
      </svg>
    </div>

    <script src="js/jquery.min.js"></script>
    <script src="js/jquery-migrate-3.0.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.easing.1.3.js"></script>
    <script src="js/jquery.waypoints.min.js"></script>
    <script src="js/jquery.stellar.min.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/jquery.magnific-popup.min.js"></script>
    <script src="js/aos.js"></script>
    <script src="js/jquery.animateNumber.min.js"></script>
    <script src="js/scrollax.min.js"></script>

    <script src="js/main.js"></script>

    <!-- CUSTOM SCRIPT FOR DYNAMIC BLOG POSTS -->
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        const blogPostsContainer = document.getElementById("blogPostsContainer");

        // If the container for dynamic blog posts isn't found, exit early.
        if (!blogPostsContainer) {
          // console.warn("Element with ID 'blogPostsContainer' not found in index.html. Dynamic blog post loading will be skipped for this page.");
          return;
        }

        async function fetchAndRenderBlogPosts() {
          if (!window.supabaseClient) {
            console.warn("Supabase client not initialized. Cannot fetch blog posts.");
            return;
          }

          try {
            const urlParams = new URLSearchParams(window.location.search);
            const categoryName = urlParams.get("category");
            let categoryId = null;

            if (categoryName) {
              // Fetch category ID by name
              const { data: categoryData, error: categoryError } = await window.supabaseClient
                .from("categories")
                .select("id")
                .eq("name", categoryName)
                .single();

              if (categoryError) {
                console.error("Error fetching category ID:", categoryError);
                // Continue without filtering if category not found
              } else if (categoryData) {
                categoryId = categoryData.id;
              }
            }

            let query = window.supabaseClient
              .from("posts")
              .select("*")
              .order("created_at", { ascending: false });

            if (categoryId) {
              query = query.eq("category_id", categoryId);
            }

            const { data: posts, error } = await query;

            if (error) {
              console.error("Error fetching blog posts:", error);
              blogPostsContainer.innerHTML = "<p>Error loading blog posts.</p>";
              return;
            }

            blogPostsContainer.innerHTML = ""; // Clear existing posts

            if (posts.length === 0) {
              blogPostsContainer.innerHTML = "<p>No blog posts found.</p>";
              return;
            }

            posts.forEach(post => {
              const postDate = new Date(post.created_at).toLocaleDateString();
              const postHtml = `
                <div class="col-md-4 d-flex ftco-animate">
                  <div class="blog-entry justify-content-end">
                    <a href="single.html?post=${post.id}" class="block-20" style="background-image: url('images/image_1.jpg');">
                    </a>
                    <div class="text mt-3 float-right d-block">
                      <div class="d-flex align-items-center mb-3 meta">
                        <p class="mb-0">
                          <span class="mr-2">${postDate}</span>
                          <a href="#" class="mr-2">${post.author}</a>
                          <a href="single.html?post=${post.id}" class="meta-chat"><span class="icon-chat"></span> 0</a>
                        </p>
                      </div>
                      <h3 class="heading"><a href="single.html?post=${post.id}">${post.title}</a></h3>
                      <p>${post.content.substring(0, 100)}...</p>
                    </div>
                  </div>
                </div>
              `;
              blogPostsContainer.insertAdjacentHTML("beforeend", postHtml);
            });

          } catch (error) {
            console.error("An unexpected error occurred while fetching blog posts:", error);
            blogPostsContainer.innerHTML = "<p>An unexpected error occurred.</p>";
          }
        }

        // Initial fetch and render of blog posts when the page loads
        fetchAndRenderBlogPosts();
      });
    </script>
  </body>
</html>
